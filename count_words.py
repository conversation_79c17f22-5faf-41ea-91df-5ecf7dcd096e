import os
import glob

def count_chapters():
    chapter_files = glob.glob('chapters/chapter13*.md') + glob.glob('chapters/chapter14*.md') + glob.glob('chapters/chapter150.md')
    results = []
    for file in sorted(chapter_files):
        with open(file, 'r', encoding='utf-8') as f:
            content = f.read()
            word_count = len(content)
            results.append(f"{os.path.basename(file)}: {word_count}字")
    return "\n".join(results)

if __name__ == "__main__":
    print(count_chapters())
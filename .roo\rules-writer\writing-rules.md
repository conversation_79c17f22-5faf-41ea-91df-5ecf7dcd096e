# 核心规划和更新频率
## 在写作前，需要提前确定好的“核心骨架”
包括以下文件：
1. [核心创意与卖点](docs/核心创意与卖点.md) 
2. [主角终极目标与核心驱动力](docs/主角终极目标与核心驱动力.md)
3. [世界观基础设定](docs/世界观基础设定.md)
4. [主线剧情框架](docs/主线剧情框架.md)

## 需要提前规划并持续维护的关键要素
包括以下文件：
1. [主角深度设定档案](docs/主角深度设定档案.md)
2. [重要配角与反派设定档案](docs/重要配角与反派设定档案.md)
3. [伏笔与悬念追踪表](docs/伏笔与悬念追踪表.md)
4. [核心设定扩展与细节库](docs/核心设定扩展与细节库.md)

## 写作过程中需要动态规划调整与记录
1. [细纲与章纲](docs/细纲与章纲.md) 
2. [情绪与节奏记录表](docs/情绪与节奏记录表.md)
3. [读者反馈和数据观察](docs/读者反馈和数据观察.md)

## 写作全过程参考技巧
 [写作技巧](docs/写作技巧.md)



# 一、 必须提前规划的核心骨架（决定故事的根基和方向）

## 核心创意与卖点 (Elevator Pitch):
文件位置：docs\核心创意与卖点.md

内容： 用1-3句话清晰概括你的小说最核心、最独特、最吸引人的地方是什么？
 - 例1： “废柴少年觉醒上古吞噬体质，靠吃天材地宝、神兵利器甚至敌人修为快速升级，在强者为尊的世界一路吃成至尊！” (核心卖点：新奇金手指+爽快升级)
 - 例2： “顶级法医重生古代仵作之女，利用现代刑侦技术屡破奇案，卷入宫廷阴谋，与冷面王爷携手揭开惊天秘案。” (核心卖点：职业金手指+悬疑探案+权谋)

作用： 这是你小说的灵魂和灯塔。所有后续规划、写作、营销都应围绕它展开。迷茫时、卡文时、偏离时，用它校准方向。

规划程度： 必须清晰、明确、固定。写作过程中可微调表达，但核心本质不宜大变。

## 主角终极目标与核心驱动力：
文件位置：docs\主角终极目标与核心驱动力.md

内容：
 - 终极目标： 主角在整个故事结束时想要达成的最终状态是什么？（复仇成功？登顶巅峰？守护家园？找到真爱？揭开身世？）
 - 核心驱动力： 是什么在持续推动主角去追求这个目标？（深仇大恨？生存压力？守护承诺？求知欲？对力量的渴望？）

作用： 提供贯穿始终的故事主线，是读者长期追读的原始动力（期待感）。主角的所有重大行动都应与此相关。

规划程度： 必须明确。写作过程中可深化其内涵，但目标本身和核心驱动力应保持稳定。


## 世界观基础设定 (冰山原则 - 只规划露出水面的部分和水下支撑结构)：
文件位置：docs\世界观基础设定.md

核心内容 (必须规划)：
 - 世界性质： 玄幻？仙侠？科幻？都市？历史？末世？西幻？游戏？
 - 核心规则/力量体系： 修炼等级（清晰简洁！）、异能来源、科技水平、魔法原理、社会运转的基本法则（如弱肉强食、律法森严）。重点在于其独特性、限制性和对剧情/人物的影响。
 - 关键阵营/势力分布： 影响世界格局的几大顶级势力（国家、宗门、家族、组织）及其核心矛盾、大致立场。
 - 时代背景/关键历史事件： 影响当前格局的大事件（如世界大战、灵气复苏、王朝更迭）。

作用： 为故事提供可信的舞台和逻辑基础。避免前后矛盾。

规划程度： 基础框架必须清晰。细节（如小门派、地方风俗、次级历史）可以在写作过程中根据需要补充，但需记录在案以保证一致性。避免过度规划无关细节！

## 主线剧情大框架 (粗纲/里程碑)：
文件位置：docs\主线剧情框架.md

内容： 将整个故事划分为几个大的阶段（如：崛起篇、扬名篇、争霸篇、终局篇），明确每个阶段的：
 - 起点： 主角进入该阶段时的状态/位置。
 - 核心冲突/主要事件： 该阶段要解决的核心问题或发生的关键大事件（如：宗门大比、探索秘境、家族危机、对抗某个大反派）。
 - 阶段目标： 主角在该阶段结束时需要达成的目标（短期目标，服务于终极目标）。
 - 终点/转折点： 该阶段结束时主角的状态/位置，以及如何引出下一阶段。
 - 关键伏笔埋设点： 在此阶段需要埋下哪些影响后续（尤其是终局）的重大伏笔？

作用： 提供故事发展的主干道，确保剧情有清晰的推进方向和节奏感，避免中期乏力或迷路。

规划程度： 需要比较清晰的框架。写作过程中，具体路径和事件细节可以调整，但大的阶段目标和转折点应尽量稳定。


# 二、 需要提前规划并持续维护的关键要素（保证角色鲜活、剧情合理）

## 主角深度设定档案：
文件位置：docs\主角深度设定档案.md

内容 (超越基础人设)：
 - 核心目标 & 驱动力 (见上，此处深化)。
 - 核心信念/价值观： 什么是主角绝对坚持的？什么是其底线？（如：家人不可欺、恩仇必报、追求绝对自由）
 - 核心缺陷/弱点： 不仅是能力上的，更是性格、心理上的（如：多疑、傲慢、情感用事、有心理阴影）。这些缺陷如何影响其决策和人际关系？如何被利用？
 - 核心恐惧： 主角内心深处最害怕什么？（失败？失去至亲？被背叛？暴露真实身份？）
 - 金手指详解： 具体能力、限制条件、升级/开发路径、潜在代价/副作用。
 - 关键成长点规划： 预计主角在能力、心智、观念上将在哪些关键节点发生重大转变？

维护： 随着剧情发展，记录主角心态、观念、关系的变化，确保成长弧线合理连贯。

## 重要配角与反派设定档案：
文件位置：docs\重要配角与反派设定档案.md

内容 (每人一档)：
 - 姓名、身份、外貌特征 (记忆点)。
与主角的核心关系 (盟友/对手/爱慕/仇恨/利用等) 及 关系演变方向。
 - 核心目标与驱动力 (独立于主角！)。
 - 核心信念/价值观/缺陷/恐惧 (理解其行为逻辑)。
 - 独特能力/资源/背景。
 - 功能性定位 (如：导师、搞笑担当、宿敌、白月光、情报源)。
 - 结局预设 (死亡？归隐？洗白？黑化？)。

维护： 记录其与主角及他人互动的关键事件、态度转变、秘密揭露等，确保行为一致性和关系发展的合理性。

## 伏笔与悬念追踪表：
文件位置：docs\伏笔与悬念追踪表.md

内容 (表格形式最佳)：
 - 伏笔/悬念内容： 具体描述 (如：神秘玉佩的裂纹、黑衣人提到的“组织”、古籍中缺失的一页)。
 - 埋设位置： 第X章。
 - 关联人物/事件： 与谁或哪个事件相关？
 - 预计回收/解答位置： 计划在第X章左右揭示。
 - 解答方式/关键信息： 计划如何解答？揭示什么核心信息？
 - 状态： 已埋设 / 已回收 / 待回收。
 - 作用： 避免遗忘伏笔，确保悬念有始有终，提升故事严谨性和读者解谜快感。是保证整体性的关键工具！

维护： 持续更新！ 每埋设一个新伏笔/悬念就记录，每回收一个就标记。定期检查是否有长期未回收的“死伏笔”。

## 核心设定扩展与细节库 (按需规划，持续补充)：
文件位置：docs\核心设定扩展与细节库.md

内容：
 - 力量/技能/物品体系详表： 等级名称、特征、代表人物/物品、获取/升级方式。 (尤其玄幻/科幻/游戏文必备)
 - 势力/组织架构图： 主要势力内部结构、重要人物、地盘分布、核心利益。
 - 地图与地理志： 关键地点（城市、宗门、秘境）的位置、特色、重要资源/危险。
 - 特殊名词/概念解释： 独创的术语、组织名称、功法名称等及其含义。
 - 时间线： 重大历史事件时间点、主角年龄/经历时间轴。
 - 作用： 保证设定细节的前后一致性，方便查阅，避免吃书。

维护： 在写作过程中，每当创造新的设定细节（如一个新地点、一种新丹药、一个组织的小头目），及时补充到对应的库中。

# 三、 写作过程中的动态规划与记录（保证章节衔接流畅、节奏可控）

## 细纲/章纲 (动态更新)：
文件位置：docs\细纲与章纲.md

内容： 在主线粗纲框架下，为接下来要写的5-10章（或一个完整的小单元/副本）规划更详细的内容：
 - 每章的核心事件/冲突。
 - 要达到的小目标（推进主线？解决小冲突？塑造人物？埋设伏笔？）。
 - 出场人物及作用。
 - 关键对话/行动点。
 - 章节结尾的钩子设计。
 - 需要回收/呼应的前期伏笔。

作用： 明确每日写作任务，保证章节内容充实有目的性，控制节奏，强化衔接。

维护： 写作前制定，写作中可微调。完成后标记。是保证日更流畅性的利器。

## 情绪/节奏记录表：
文件位置：docs\情绪与节奏记录表.md

内容 (简单记录)：
 - 标记关键章节（尤其是高潮、转折、重大情感爆发点）的位置和类型（如：小爽点、大高潮、虐点、温馨日常）。
 - 简单记录该章节试图营造的主要情绪（紧张、欢乐、悲伤、燃、悬疑）。
 - 自我评估或根据读者反馈/数据标注该章节的节奏快慢。

作用： 直观掌握全书情绪起伏和节奏变化，避免长时间平淡或持续高压，确保张弛有度。有助于优化后续章节安排。

维护： 每写完一个重要章节或单元后进行记录。

## 读者反馈/数据观察笔记：
文件位置：docs\读者反馈和数据观察.md

内容：
 - 用户记录重要章节发布后的读者评论热点（好评点、吐槽点、疑问点、预测）。
 - 关注后台数据变化（收藏、追读、章节完读率），特别是异常波动（如某章完读率骤降）。
 - 分析原因（剧情毒点？节奏问题？人设崩塌？钩子无力？）。

作用： 了解读者喜好和市场反应，为后续剧情微调、优化节奏、强化优点、规避雷点提供依据。但需注意： 核心主线和大纲不宜被个别评论轻易动摇，需综合判断。

维护： 定期（如每周）整理分析。
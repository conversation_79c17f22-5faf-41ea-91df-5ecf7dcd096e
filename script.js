class NovelReader {
    constructor() {
        this.currentChapter = 0;
        this.chapters = [
            { file: 'chapters/chapter1.md', title: '第一章：平凡的一天与突变的开始' },
            { file: 'chapters/chapter2.md', title: '第二章：反向系统的惩罚机制' },
            { file: 'chapters/chapter3.md', title: '第三章：求生之路：寻找木匠师傅' },
            { file: 'chapters/chapter4.md', title: '第四章：老街的方老爷子' },
            { file: 'chapters/chapter5.md', title: '第五章：入门：劈柴与榫卯' },
            { file: 'chapters/chapter6.md', title: '第六章：异能的首次奇效' },
            { file: 'chapters/chapter7.md', title: '第七章：新世界的大门' },
            { file: 'chapters/chapter8.md', title: '第八章：铁画银钩初体验' },
            { file: 'chapters/chapter9.md', title: '第九章：系统的新任务与困境' },
            { file: 'chapters/chapter10.md', title: '第十章：都市寻材与张胖子' },
            { file: 'chapters/chapter11.md', title: '第十一章：废弃工厂里的火光' },
            { file: 'chapters/chapter12.md', title: '第十二章：自学与摸索中的进步' },
            { file: 'chapters/chapter13.md', title: '第十三章：偶然的委托：修复旧物' },
            { file: 'chapters/chapter14.md', title: '第十四章：手艺与异能的结合应用' },
            { file: 'chapters/chapter15.md', title: '第十五章：小有名气与新的挑战' },
            { file: 'chapters/chapter16.md', title: '第十六章：苏婉清的出现' },
            { file: 'chapters/chapter17.md', title: '第十七章：传统与创新的碰撞' },
            { file: 'chapters/chapter18.md', title: '第十八章：系统的隐藏提示' },
            { file: 'chapters/chapter19.md', title: '第十九章：铁艺精通与新的能力解锁' },
            { file: 'chapters/chapter20.md', title: '第二十章：都市暗流的初次感知' },
            { file: 'chapters/chapter21.md', title: '第二十一章：第三个手艺任务：玉雕？' },
            { file: 'chapters/chapter22.md', title: '第二十二章：拜师路上的艰难' },
            { file: 'chapters/chapter23.md', title: '第二十三章：与苏婉清的合作或竞争' },
            { file: 'chapters/chapter24.md', title: '第二十四章：玉石的秘密' },
            { file: 'chapters/chapter25.md', title: '第二十五章：来自“收藏家”的试探' },
            { file: 'chapters/chapter26.md', title: '第二十六章：首次超凡冲突的边缘' },
            { file: 'chapters/chapter27.md', title: '第二十七章：能力的新运用方式' },
            { file: 'chapters/chapter28.md', title: '第二十八章：手艺带来的声誉与麻烦' },
            { file: 'chapters/chapter29.md', title: '第二十九章：系统规则的深层解读' },
            { file: 'chapters/chapter30.md', title: '第三十章：为下一阶段蓄力' },
            { file: 'chapters/chapter31.md', title: '第三十一章：风云前夕与“子夜集”邀约' },
            { file: 'chapters/chapter32.md', title: '第三十二章：“玉痴翁”的松动与“聚宝楼”之行' },
            { file: 'chapters/chapter33.md', title: '第三十三章：文老的指点与拍卖会风声' },
            { file: 'chapters/chapter34.md', title: '第三十四章：“子夜集”风云再起' },
            { file: 'chapters/chapter35.md', title: '第三十五章：金缕玉之争与意外援手' },
            { file: 'chapters/chapter36.md', title: '第三十六章：红袖的橄榄枝与玉中秘辛' },
            { file: 'chapters/chapter37.md', title: '第三十七章：师徒联手与“玉心”初探' },
            { file: 'chapters/chapter38.md', title: '第三十八章：符文与玉雕的初次融合' },
            { file: 'chapters/chapter39.md', title: '第三十九章：“空冥石”现世与风波骤起' },
            { file: 'chapters/chapter40.md', title: '第四十章：黑暗中的博弈与鲁班尺之威' },
            { file: 'chapters/chapter41.md', title: '第四十一章：风波暂歇与“玉髓龙涎”' },
            { file: 'chapters/chapter42.md', title: '第四十二章：心灯佛的雕琢与异象初显' },
            { file: 'chapters/chapter43.md', title: '第四十三章：玉髓点睛与法器初成' },
            { file: 'chapters/chapter44.md', title: '第四十四章：系统升级与新的征程' },
            { file: 'chapters/chapter45.md', title: '第四十五章：“匠心宝匣”与意外的“传承”' },
            { file: 'chapters/chapter46.md', title: '第四十六章：鲁班玉典与修行新章' },
            { file: 'chapters/chapter47.md', title: '第四十七章：匠神心诀与玉典初悟' },
            { file: 'chapters/chapter48.md', title: '第四十八章：“种玉术”初试与“会呼吸”的玉' },
            { file: 'chapters/chapter49.md', title: '第四十九章：“活玉”之秘与《鲁班玉典》新篇' },
            { file: 'chapters/chapter50.md', title: '第五十章：乙木青龙佩的设计与风起云涌' },
            { file: 'chapters/chapter51.md', title: '第五十一章：青龙佩的诞生与技艺飞跃' },
            { file: 'chapters/chapter52.md', title: '第五十二章：系统再升级与“器灵”之谜' },
            { file: 'chapters/chapter53.md', title: '第五十三章：鲁班秘境的线索与新的危机' },
            { file: 'chapters/chapter54.md', title: '第五十四章：墨斗寻龙与卧龙山之险' },
            { file: 'chapters/chapter55.md', title: '第五十五章：峡谷鏖战与内鬼疑云' },
            { file: 'chapters/chapter56.md', title: '第五十六章：绝境逢生与秘境之门' },
            { file: 'chapters/chapter57.md', title: '第五十七章：秘境之内与“传承试炼”' },
            { file: 'chapters/chapter58.md', title: '第五十八章：通天塔第一层：机关迷阵' },
            { file: 'chapters/chapter59.md', title: '第五十九章：第二层的考验：“傀儡问心”' },
            { file: 'chapters/chapter60.md', title: '第六十章：技惊阿木与青青的心事' },
            { file: 'chapters/chapter61.md', title: '第六十一章：玉雕大成与丝绣传承' },
            { file: 'chapters/chapter62.md', title: '第六十二章：苏家绣谱与婉清的决心' },
            { file: 'chapters/chapter63.md', title: '第六十三章：针尖上的修行与异能新境界' },
            { file: 'chapters/chapter64.md', title: '第六十四章：姑苏寻丝与绣坊风波' },
            { file: 'chapters/chapter65.md', title: '第六十五章：丝线为刃与绣坊新主' },
            { file: 'chapters/chapter66.md', title: '第六十六章：灵泉激活与丝线蜕变' },
            { file: 'chapters/chapter67.md', title: '第六十七章：古法染丝与青龙帮的觊觎' },
            { file: 'chapters/chapter68.md', title: '第六十八章：丝之领域与不速之客' },
            { file: 'chapters/chapter69.md', title: '第六十九章：天工绣谱的残篇与苏绣新境' },
            { file: 'chapters/chapter70.md', title: '第七十章：面具人的身份与“玄冥令”' },
            { file: 'chapters/chapter71.md', title: '第七十一章：文老的邀约与“璞玉迷踪”' },
            { file: 'chapters/chapter72.md', title: '第七十二章：解石风云与鲁班尺显威' },
            { file: 'chapters/chapter73.md', title: '第七十三章：储物法器的诞生与“子夜集”请柬' },
            { file: 'chapters/chapter74.md', title: '第七十四章：再临子夜集与暗流涌动' },
            { file: 'chapters/chapter75.md', title: '第七十五章：冰种灵玉与神秘残图' },
            { file: 'chapters/chapter76.md', title: '第七十六章：残图到手与空冥石现身' },
            { file: 'chapters/chapter77.md', title: '第七十七章：空冥母矿的疯狂与渔翁之利' },
            { file: 'chapters/chapter78.md', title: '第七十八章：虎口夺食与空间异动' },
            { file: 'chapters/chapter79.md', title: '第七十九章：空间风暴与神秘援手' },
            { file: 'chapters/chapter80.md', title: '第八十章：风波平息与各方反应' },
            { file: 'chapters/chapter81.md', title: '第八十一章：闭关炼化与苏绣新思路' },
            { file: 'chapters/chapter82.md', title: '第八十二章：星空绣卷与空间之力初显' },
            { file: 'chapters/chapter83.md', title: '第八十三章：星图完成与异象初现' },
            { file: 'chapters/chapter84.md', title: '第八十四章：神秘窥探与绣卷的“副作用”' },
            { file: 'chapters/chapter85.md', title: '第八十五章：绣卷初试与新的征程' },
            { file: 'chapters/chapter86.md', title: '第八十六章：系统升级与“匠神之路”' },
            { file: 'chapters/chapter87.md', title: '第八十七章：初探炼器与阵法模块' },
            { file: 'chapters/chapter88.md', title: '第八十八章：首次炼器：空间道标的升华' },
            { file: 'chapters/chapter89.md', title: '第八十九章：阵法初解：绣谱中的奥秘' },
            { file: 'chapters/chapter90.md', title: '第九十章：“洞天福地”的宏伟蓝图' },
            { file: 'chapters/chapter91.md', title: '第九十一章：材料之难：寻访“息壤”与“建木”' },
            { file: 'chapters/chapter92.md', title: '第九十二章：张胖子的情报网与意外线索' },
            { file: 'chapters/chapter93.md', title: '第九十三章：古玩市场的“捡漏”风波' },
            { file: 'chapters/chapter94.md', title: '第九十四章：苏婉清的阵法天赋' },
            { file: 'chapters/chapter95.md', title: '第九十五章：神秘拍卖会与“息壤”的踪迹' },
            { file: 'chapters/chapter96.md', title: '第九十六章：竞拍风云与隐藏的对手' },
            { file: 'chapters/chapter97.md', title: '第九十七章：“息壤”到手与新的危机' },
            { file: 'chapters/chapter98.md', title: '第九十八章：都市追逐战与空间掌控初显威' },
            { file: 'chapters/chapter99.md', title: '第九十九章：化险为夷与“建木”的秘密' },
            { file: 'chapters/chapter100.md', title: '第一百章：百年树心与灵植培育之术' },
            { file: 'chapters/chapter101.md', title: '第一百零一章：开辟“芥子空间”：洞天雏形' },
            { file: 'chapters/chapter102.md', title: '第一百零二章：灵气汇聚与空间法则的感悟' },
            { file: 'chapters/chapter103.md', title: '第一百零三章：“匠心空间”的妙用与升级' },
            { file: 'chapters/chapter104.md', title: '第一百零四章：种植灵药与酿造灵酒的尝试' },
            { file: 'chapters/chapter105.md', title: '第一百零五章：灵酒“醉仙霖”与意外访客' },
            { file: 'chapters/chapter106.md', title: '第一百零六章：方老爷子的指点与“匠心”真意' },
            { file: 'chapters/chapter107.md', title: '第一百零七章：天工绣谱新篇章：阵绣合一' },
            { file: 'chapters/chapter108.md', title: '第一百零八章：炼制本命法宝的构想' },
            { file: 'chapters/chapter109.md', title: '第一百零九章：四方阁的再次邀请' },
            { file: 'chapters/chapter110.md', title: '第一百一十章：文博的求助与“镇龙桩”之谜' },
            { file: 'chapters/chapter111.md', title: '第一百一十一章：地脉异动与都市危机初现' },
            { file: 'chapters/chapter112.md', title: '第一百一十二章：联手探查与诡异的地下空洞' },
            { file: 'chapters/chapter113.md', title: '第一百一十三章：“玄冥教”的阴谋再现' },
            { file: 'chapters/chapter114.md', title: '第一百一十四章：修复“镇龙桩”：巧匠的挑战' },
            { file: 'chapters/chapter115.md', title: '第一百一十五章：阵法、炼器、苏绣的完美融合' },
            { file: 'chapters/chapter116.md', title: '第一百一十六章：“镇龙桩”重光与地脉平息' },
            { file: 'chapters/chapter117.md', title: '第一百一十七章：玄冥教护法的截杀' },
            { file: 'chapters/chapter118.md', title: '第一百一十八章：“周天星斗幻域图”VS玄冥鬼火' },
            { file: 'chapters/chapter119.md', title: '第一百一十九章：空间绝杀与强敌授首' },
            { file: 'chapters/chapter120.md', title: '第一百二十章：名声鹊起与各方震动' },
            { file: 'chapters/chapter121.md', title: '第一百二十一章：“洞天福地”的进一步完善' },
            { file: 'chapters/chapter122.md', title: '第一百二十二章：培养“空冥蚕”与炼制“云霞羽衣”' },
            { file: 'chapters/chapter123.md', title: '第一百二十三章：苏婉清的心意与双修之议？' },
            { file: 'chapters/chapter124.md', title: '第一百二十四章：来自修真界的邀请函' },
            { file: 'chapters/chapter125.md', title: '第一百二十五章：新的征程：手艺人的修仙界大冒险' },
            { file: 'chapters/chapter126.md', title: '第一百二十六章：星环古道与械族文明' },
            { file: 'chapters/chapter127.md', title: '第一百二十七章：熔炉真相与械族之叛' },
            { file: 'chapters/chapter128.md', title: '第一百二十八章：星环枢纽与时间织女' },
            { file: 'chapters/chapter129.md', title: '第一百二十九章：时空悖论与因果闭环' },
            { file: 'chapters/chapter130.md', title: '第一百三十章：星海匠盟与碑文起源' },
            { file: 'chapters/chapter131.md', title: '第一百三十一章：匠神归位与星环新生' },
            { file: 'chapters/chapter132.md', title: '第一百三十二章：火星工坊与远古机甲' },
            { file: 'chapters/chapter133.md', title: '第一百三十三章：赤道峡谷的青铜心跳' },
            { file: 'chapters/chapter134.md', title: '第一百三十四章：九碑结界与匠魂觉醒' },
            { file: 'chapters/chapter135.md', title: '第一百三十五章：文明火种与绝望污染' },
            { file: 'chapters/chapter136.md', title: '第一百三十六章：匠心永恒与星火传承' },
            { file: 'chapters/chapter137.md', title: '第一百三十七章：创世匠艺与熵增终战' },
            { file: 'chapters/chapter138.md', title: '第一百三十八章：归墟与新程' },
            { file: 'chapters/chapter139.md', title: '第一百三十九章：匠道纪元与火星共荣' },
            { file: 'chapters/chapter140.md', title: '第一百四十章：菌丝革命与星绣霓裳' },
            { file: 'chapters/chapter141.md', title: '第一百四十一章：星路逆行与熵增使徒' },
            { file: 'chapters/chapter142.md', title: '第一百四十二章：九碑合璧与维度潜航' },
            { file: 'chapters/chapter143.md', title: '第一百四十三章：时间奇点与匠神归来' },
            { file: 'chapters/chapter144.md', title: '第一百四十四章：星炬纪元与传承之火' },
            { file: 'chapters/chapter145.md', title: '第一百四十五章：熵增牢笼与希望刻刀' },
            { file: 'chapters/chapter146.md', title: '第一百四十六章：星环血战与血脉觉醒' },
            { file: 'chapters/chapter147.md', title: '第一百四十七章：宇宙壁垒与希望微光' },
            { file: 'chapters/chapter148.md', title: '第一百四十八章：负熵之光与血脉传承' },
            { file: 'chapters/chapter149.md', title: '第一百四十九章：星尘纪元与传承之路' },
            { file: 'chapters/chapter150.md', title: '第一百五十章：匠心永恒与星火传承' },
            { file: 'chapters/chapter151.md', title: '第一百五十一章：熵增重生与陶歌危局' },
            { file: 'chapters/chapter152.md', title: '第一百五十二章：菌丝文明与守护之誓' },
            { file: 'chapters/chapter153.md', title: '第一百五十三章：时之茧房与共振突破' },
            { file: 'chapters/chapter154.md', title: '第一百五十四章：文明火种与星盟基石' },
            { file: 'chapters/chapter155.md', title: '第一百五十五章：情感共鸣理论突破' },
            { file: 'chapters/chapter156.md', title: '第一百五十六章：时间织女的觉醒' },
            { file: 'chapters/chapter157.md', title: '第一百五十七章：时间契约的诅咒' },
            { file: 'chapters/chapter158.md', title: '第一百五十八章：机械共鸣终极测试' },
            { file: 'chapters/chapter159.md', title: '第一百五十九章：起点保卫战' },
            { file: 'chapters/chapter160.md', title: '第一百六十章：残部集结与契约真相' },
            { file: 'chapters/chapter161.md', title: '第一百六十一章：螳螂星域的背叛' },
            { file: 'chapters/chapter162.md', title: '第一百六十二章：远古守护者苏醒' },
            { file: 'chapters/chapter163.md', title: '第一百六十三章：星盟誓师典礼' },
            { file: 'chapters/chapter164.md', title: '第一百六十四章：情感熔炉与武器原型' },
            { file: 'chapters/chapter165.md', title: '第一百六十五章：跨时空匠神网络' },
            { file: 'chapters/chapter166.md', title: '第一百六十六章：武器实战检验' },
            { file: 'chapters/chapter167.md', title: '第一百六十七章：匠神碑共鸣仪式' },
            { file: 'chapters/chapter168.md', title: '第一百六十八章：情感熔炉最终调试' },
            { file: 'chapters/chapter169.md', title: '第一百六十九章：星网初啼' },
            { file: 'chapters/chapter170.md', title: '第一百七十章：守护者之誓' },
            { file: 'chapters/chapter171.md', title: '第一百七十一章：暗流涌动' },
            { file: 'chapters/chapter172.md', title: '第一百七十二章：时管局防线突破' },
            { file: 'chapters/chapter173.md', title: '第一百七十三章：熵增核心显露' },
            { file: 'chapters/chapter174.md', title: '第一百七十四章：时间回廊迷局' },
            { file: 'chapters/chapter175.md', title: '第一百七十五章：创世匠艺终击' },
            { file: 'chapters/chapter176.md', title: '第一百七十六章：碑文重铸与法则编织' },
            { file: 'chapters/chapter177.md', title: '第一百七十七章：跨宇宙匠道网络' },
            { file: 'chapters/chapter178.md', title: '第一百七十八章：织时者使命' },
            { file: 'chapters/chapter179.md', title: '第一百七十九章：守护者之眠' },
            { file: 'chapters/chapter180.md', title: '第一百八十章：自由匠歌' }
        ];
        this.init();
    }

    init() {
        this.bindEvents();
        this.generateChapterList();
        this.updateDateTime(); 
    }

    bindEvents() {
        const startBtn = document.getElementById('start-reading');
        const backBtn = document.getElementById('back-to-index');
        const prevBtn = document.getElementById('prev-chapter');
        const nextBtn = document.getElementById('next-chapter');
        const themeToggle = document.getElementById('theme-toggle');

        if (startBtn) startBtn.addEventListener('click', () => this.loadChapter(0));
        if (backBtn) backBtn.addEventListener('click', () => this.showIndex());
        if (prevBtn) prevBtn.addEventListener('click', () => this.loadChapter(this.currentChapter - 1));
        if (nextBtn) nextBtn.addEventListener('click', () => this.loadChapter(this.currentChapter + 1));
        if (themeToggle) themeToggle.addEventListener('click', () => this.toggleTheme());
    }

    generateChapterList() {
        const chapterList = document.getElementById('chapter-list');
        if (!chapterList) return;
        chapterList.innerHTML = '';

        this.chapters.forEach((chapter, index) => {
            const item = document.createElement('div');
            item.className = 'p-3 rounded-lg hover:bg-purple-50 cursor-pointer transition-colors border border-transparent hover:border-purple-200';
            item.innerHTML = `
                <div class="text-sm font-medium text-slate-700">${chapter.title}</div>
                <div class="text-xs text-slate-500 mt-1">第${index + 1}章</div>
            `;
            item.addEventListener('click', () => this.loadChapter(index));
            chapterList.appendChild(item);
        });
    }

    async loadChapter(index) {
        if (index < 0 || index >= this.chapters.length) return;

        this.showLoading();
        this.currentChapter = index;

        try {
            const chapter = this.chapters[index];
            const response = await fetch(chapter.file);
            if (!response.ok) throw new Error(`Failed to load chapter: ${response.statusText} (file: ${chapter.file})`);
            
            const content = await response.text();
            const { metadata, html } = this.parseMarkdown(content, chapter.file); // Pass chapter.file for logging
            
            this.displayChapter(metadata, html);
            this.updateNavigation();
            this.bindCopyButtons();
            
        } catch (error) {
            console.error('Error loading chapter:', error);
            this.showError(`加载章节失败: ${error.message}`);
        } finally {
            this.hideLoading();
        }
    }

    parseMarkdown(content, filePath = "unknown file") {
        // Regex to match YAML front matter
        // Handles \r\n (CRLF) and \n (LF) newlines
        // m flag for multiline ^ and $
        const frontMatterRegex = /^---\r?\n([\s\S]*?)\r?\n---\r?\n([\s\S]*)$/;
        const frontMatterMatch = content.match(frontMatterRegex);

        let metadata = {};
        let markdownString;

        if (frontMatterMatch) {
            const yamlString = frontMatterMatch[1];
            markdownString = frontMatterMatch[2] || ""; // Ensure markdownString is not undefined
            try {
                metadata = jsyaml.load(yamlString) || {};
            } catch (e) {
                console.error(`YAML parsing error in ${filePath}:`, e);
                metadata = { title: "元数据解析错误", author: "未知", date: "" }; // Provide default metadata on error
            }
        } else {
            // No valid front matter found, or it's malformed. Treat entire content as markdown.
            console.warn(`Front matter not found or malformed in ${filePath}. Treating entire content as markdown.`);
            markdownString = content;
            // Attempt to infer title from first H1, otherwise use a default
            const h1Match = markdownString.match(/^#\s*(.*)/m);
            if (h1Match && h1Match[1]) {
                metadata.title = h1Match[1].trim();
            } else {
                metadata.title = "章节（无元数据标题）";
            }
            metadata.author = metadata.author || "未知作者";
            metadata.date = metadata.date || "";
        }

        try {
            const html = marked.parse(markdownString);
            return { metadata, html };
        } catch (error) {
            console.error(`Error parsing markdown for ${filePath}:`, error);
            return { metadata, html: `<p>解析Markdown内容时出错: ${error.message}</p>` }; // Return existing metadata with error message
        }
    }

    displayChapter(metadata, html) {
        const chapterContent = document.getElementById('chapter-content');
        const chapterTitle = document.getElementById('chapter-title');
        const chapterAuthor = document.getElementById('chapter-author');
        const chapterDate = document.getElementById('chapter-date');
        const chapterWords = document.getElementById('chapter-words');
        const chapterText = document.getElementById('chapter-text');
        const indexCard = document.querySelector('.bg-white\\/70.backdrop-blur-sm.rounded-2xl.p-8.shadow-lg.border.border-slate-200.mb-8');


        if (!chapterContent || !chapterTitle || !chapterAuthor || !chapterDate || !chapterWords || !chapterText || !indexCard) {
            console.error('One or more chapter display elements are missing from the DOM.');
            return;
        }

        chapterTitle.textContent = metadata.title || this.chapters[this.currentChapter].title || "未知标题";
        chapterAuthor.textContent = `作者：${metadata.author || 'AI创作'}`;
        chapterDate.textContent = metadata.date || new Date().toLocaleDateString();
        
        const wordCount = html.replace(/<[^>]*>/g, '').length;
        chapterWords.textContent = `字数：${wordCount}`;

        chapterText.innerHTML = html;
        
        chapterContent.classList.remove('hidden');
        indexCard.classList.add('hidden');
        
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    updateNavigation() {
        const prevBtn = document.getElementById('prev-chapter');
        const nextBtn = document.getElementById('next-chapter');

        if (!prevBtn || !nextBtn) return;

        prevBtn.disabled = this.currentChapter === 0;
        nextBtn.disabled = this.currentChapter === this.chapters.length - 1;

        prevBtn.classList.toggle('opacity-50', prevBtn.disabled);
        prevBtn.classList.toggle('cursor-not-allowed', prevBtn.disabled);
        nextBtn.classList.toggle('opacity-50', nextBtn.disabled);
        nextBtn.classList.toggle('cursor-not-allowed', nextBtn.disabled);
    }

    showIndex() {
        const chapterContent = document.getElementById('chapter-content');
        const indexCard = document.querySelector('.bg-white\\/70.backdrop-blur-sm.rounded-2xl.p-8.shadow-lg.border.border-slate-200.mb-8');
        
        if (!chapterContent || !indexCard) return;

        chapterContent.classList.add('hidden');
        indexCard.classList.remove('hidden');
        
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    showLoading() {
        const loadingElement = document.getElementById('loading');
        if (loadingElement) loadingElement.classList.remove('hidden');
    }

    hideLoading() {
        const loadingElement = document.getElementById('loading');
        if (loadingElement) loadingElement.classList.add('hidden');
    }

    showError(message) {
        console.error(message); 
        const chapterText = document.getElementById('chapter-text');
        if(chapterText) {
            chapterText.innerHTML = `<p class="text-red-500 font-semibold">${message}</p>`;
        }
        const chapterContent = document.getElementById('chapter-content');
        if(chapterContent) chapterContent.classList.remove('hidden');
        
        const indexCard = document.querySelector('.bg-white\\/70.backdrop-blur-sm.rounded-2xl.p-8.shadow-lg.border.border-slate-200.mb-8');
        if(indexCard) indexCard.classList.add('hidden');
    }

    toggleTheme() {
        document.body.classList.toggle('dark-theme'); 
        console.log("Theme toggled. Current body classes:", document.body.className);
    }

    updateDateTime() {
        const chapterDateEl = document.getElementById('chapter-date');
        if (chapterDateEl && !chapterDateEl.textContent) { 
             const now = new Date();
             chapterDateEl.textContent = now.toLocaleDateString();
        }
    }

    bindCopyButtons() {
        // 复制正文
        const copyTextBtn = document.getElementById('copy-chapter-text');
        if (copyTextBtn) {
            copyTextBtn.onclick = function() {
                const textElem = document.getElementById('chapter-text');
                if (textElem) {
                    let text = textElem.innerText;
                    // 跳过第一行
                    let lines = text.split('\n');
                    if (lines.length > 1) {
                        text = lines.slice(1).join('\n').trim();
                    }
                    navigator.clipboard.writeText(text).then(() => {
                        copyTextBtn.textContent = '复制成功！';
                        setTimeout(() => {
                            copyTextBtn.textContent = '一键复制正文';
                        }, 1500);
                    }).catch(() => {
                        copyTextBtn.textContent = '复制失败';
                    });
                }
            };
        }

        // 复制标题
        const copyTitleBtn = document.getElementById('copy-chapter-title');
        if (copyTitleBtn) {
            copyTitleBtn.onclick = function() {
                const titleElem = document.getElementById('chapter-title');
                if (titleElem) {
                    let title = titleElem.innerText;
                    // 只复制冒号后的内容
                    let idx = title.indexOf('：');
                    if (idx === -1) idx = title.indexOf(':');
                    if (idx !== -1 && idx < title.length - 1) {
                        title = title.slice(idx + 1).trim();
                    }
                    navigator.clipboard.writeText(title).then(() => {
                        copyTitleBtn.textContent = '复制成功！';
                        setTimeout(() => {
                            copyTitleBtn.textContent = '复制标题';
                        }, 1500);
                    }).catch(() => {
                        copyTitleBtn.textContent = '复制失败';
                    });
                }
            };
        }
    }
}

document.addEventListener('DOMContentLoaded', () => {
    new NovelReader();

    // Styles are moved to style.css or handled by Tailwind, but keeping this logic for dark theme toggle support via JS class
    // This dynamic style injection is generally fine for simple theme toggles if not using a CSS preprocessor or more complex setup.
    const style = document.createElement('style');
    style.textContent = `
        html {
            scroll-behavior: smooth;
        }
        /* Basic dark theme example, assumes Tailwind is primary */
        .dark-theme {
            /* Apply dark theme variables or overrides here if needed beyond Tailwind's dark: prefix */
        }
        .dark-theme body { /* More specific example */
            background-color: #1f2937; /* gray-800 */
            color: #f3f4f6; /* gray-100 */
        }
        .dark-theme .bg-slate-50, .dark-theme .bg-slate-100, .dark-theme .bg-white\\/80, .dark-theme .bg-white\\/70 {
            background-color: #374151 !important; /* gray-700 for cards/nav */
        }
         .dark-theme .text-slate-800, .dark-theme .text-slate-700, .dark-theme .text-slate-600, .dark-theme .text-slate-500 {
            color: #d1d5db !important; /* gray-300 for text */
        }
        .dark-theme .border-slate-200 {
            border-color: #4b5563 !important; /* gray-600 for borders */
        }
        .dark-theme .hover\\:bg-slate-100:hover {
            background-color: #4b5563 !important; /* gray-600 for hover */
        }
        .dark-theme .hover\\:bg-purple-50:hover {
            background-color: rgba(167, 139, 250, 0.2) !important; /* purple-300 with alpha for dark */
        }
        .dark-theme .hover\\:border-purple-200:hover {
            border-color: rgba(167, 139, 250, 0.5) !important; /* purple-300 with alpha for dark */
        }
        .dark-theme #theme-toggle svg {
            fill: #f3f4f6; /* gray-100 for icon */
        }
        .dark-theme .prose { /* Example for prose in dark mode */
            color: #d1d5db;
        }
        .dark-theme .prose h1, .dark-theme .prose h2, .dark-theme .prose h3, .dark-theme .prose strong {
            color: #f3f4f6;
        }
        .dark-theme .prose blockquote {
            border-left-color: #a78bfa; /* purple-400 */
            background-color: rgba(76, 29, 149, 0.2); /* Darker purple bg */
            color: #e5e7eb; /* gray-200 */
        }
    `;
    document.head.appendChild(style);
});

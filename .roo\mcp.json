{"mcpServers": {"server-sequential-thinking": {"command": "node", "args": ["C:\\nvm4w\\nodejs\\node_modules\\@modelcontextprotocol\\server-sequential-thinking\\dist\\index.js"], "alwaysAllow": ["sequentialthinking"]}, "fetch": {"command": "node", "args": ["C:\\nvm4w\\nodejs\\node_modules\\fetch-mcp\\dist\\index.js"], "disabled": true, "alwaysAllow": []}, "filesystem": {"command": "cmd", "args": ["/c", "npx", "@modelcontextprotocol/server-filesystem", "D:\\11_WorkSpace\\Novel Writter\\202505-小说-手艺人修仙指南"], "alwaysAllow": ["read_file", "read_multiple_files", "write_file", "edit_file", "create_directory", "list_directory", "directory_tree", "move_file", "search_files", "get_file_info", "list_allowed_directories"]}, "desktop-commander": {"command": "cmd", "args": ["/c", "npx", "@smithery/cli@latest", "run", "@wonderwhy-er/desktop-commander", "--key", "594ddec3-a1a6-4701-a5cf-e8ca41651def"], "alwaysAllow": ["get_config", "set_config_value", "read_file", "read_multiple_files", "write_file", "create_directory", "list_directory", "move_file", "search_files", "search_code", "get_file_info", "edit_block", "execute_command", "read_output", "force_terminate", "list_sessions", "kill_process"]}, "context7": {"command": "cmd", "args": ["/c", "npx", "@upstash/context7-mcp@latest"], "disabled": true, "alwaysAllow": []}}}